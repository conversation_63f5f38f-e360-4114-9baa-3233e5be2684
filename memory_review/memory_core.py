import os
import time

import httpx
from dotenv import load_dotenv
from mem0.configs.base import EmbedderConfig, LlmConfig, MemoryConfig, VectorStoreConfig
from openai import OpenAI
from opensearchpy import OpenSearch

load_dotenv(override=True)


class ClientFactory:
    """客户端工厂类，负责创建各种客户端实例."""

    @staticmethod
    def create_llm_client() -> OpenAI:
        """创建LLM客户端."""
        return OpenAI(
            base_url=os.getenv("QWQ_BASE_URL"),
            api_key=os.getenv("QWQ_API_KEY"),
            http_client=httpx.Client(verify=False, timeout=60.0),
        )

    @staticmethod
    def create_embedding_client() -> OpenAI:
        """创建Embedding客户端."""
        return OpenAI(
            base_url=os.getenv("EMBEDDING_BASE_URL"),
            api_key=os.getenv("EMBEDDING_API_KEY"),
            http_client=httpx.Client(verify=False, timeout=30.0),
        )

    @staticmethod
    def create_opensearch_client() -> OpenSearch:
        """创建OpenSearch客户端."""
        return OpenSearch(
            hosts=[
                {
                    "host": os.getenv("OPENSEARCH_HOST"),
                    "port": int(os.getenv("OPENSEARCH_PORT", "443")),
                }
            ],
            http_auth=(
                os.getenv("OPENSEARCH_USERNAME"),
                os.getenv("OPENSEARCH_PASSWORD"),
            ),
            use_ssl=True,
            verify_certs=False,
            ssl_assert_hostname=False,
            ssl_show_warn=False,
            pool_maxsize=20,
        )


class ConfigManager:
    """配置管理器，负责创建Memory配置."""

    @staticmethod
    def create_memory_config(
        llm_client: OpenAI,
        embedding_client: OpenAI,
        collection_name: str
    ) -> MemoryConfig:
        """创建Memory配置."""
        llm_config = LlmConfig(
            provider="openai",
            config={
                "model": os.getenv("MODEL_NAME", "gpt-4o-mini"),
                "api_key": llm_client.api_key,
                "openai_base_url": str(llm_client.base_url),
            },
        )

        embedder_config = EmbedderConfig(
            provider="openai",
            config={
                "model": os.getenv("EMBEDDING_MODEL", "text-embedding-3-small"),
                "api_key": embedding_client.api_key,
                "openai_base_url": str(embedding_client.base_url),
                "embedding_dims": None,
                "output_dimensionality": None,
            },
        )

        vector_store_config = VectorStoreConfig(
            provider="opensearch",
            config={
                "host": os.getenv("OPENSEARCH_HOST", "localhost"),
                "port": int(os.getenv("OPENSEARCH_PORT", "443")),
                "http_auth": (
                    os.getenv("OPENSEARCH_USERNAME", "admin"),
                    os.getenv("OPENSEARCH_PASSWORD", "admin"),
                ),
                "collection_name": collection_name,
                "verify_certs": False,
                "use_ssl": True,
            },
        )

        return MemoryConfig(
            llm=llm_config,
            embedder=embedder_config,
            vector_store=vector_store_config,
            version="v0.1",
        )


class IndexManager:
    """索引管理器，负责OpenSearch索引相关操作."""

    def __init__(self, vector_store, collection_name: str):
        self.vector_store = vector_store
        self.collection_name = collection_name

    def ensure_correct_index(self):
        """确保OpenSearch索引配置正确."""
        try:
            client = self.vector_store.client
            index_name = self.collection_name

            # 检查索引是否存在
            if client.indices.exists(index=index_name):
                # 获取当前索引映射
                mapping = client.indices.get_mapping(index=index_name)
                current_mapping = mapping.get(index_name, {}).get('mappings', {})

                # 检查 vector_field 的维度配置
                vector_field_props = current_mapping.get('properties', {}).get('vector_field', {})
                current_dims = vector_field_props.get('dimension')
                expected_dims = self.vector_store.embedding_model_dims

                if current_dims and current_dims != expected_dims:
                    print(f"   🔧 检测到维度不匹配: 当前={current_dims}, 期望={expected_dims}")
                    print(f"   🗑️ 删除旧索引: {index_name}")
                    client.indices.delete(index=index_name)
                    print(f"   ✅ 旧索引已删除，系统将自动创建新索引")

        except Exception as e:
            # 索引操作失败不影响主要功能
            print(f"   ⚠️ 索引操作失败: {str(e)[:50]}...")


class SafeDeleteMixin:
    """安全删除功能混入类."""

    def safe_delete(self, memory_id: str, verbose: bool = False, retry_count: int = 2):
        """安全的删除方法，绕过 mem0 的删除 bug."""
        try:
            client = self.vector_store.client
            index_name = self.collection_name

            # 刷新索引以确保最新数据可见
            try:
                client.indices.refresh(index=index_name)
            except:
                pass  # 刷新失败不影响主要功能

            if verbose:
                # 只在详细模式下显示文档结构
                print(f"   🔍 查看索引 {index_name} 中的文档结构...")
                all_docs = client.search(index=index_name, body={"query": {"match_all": {}}, "size": 3})

                if all_docs.get('hits', {}).get('hits'):
                    print("   📋 找到的文档示例:")
                    for i, hit in enumerate(all_docs['hits']['hits'][:2]):
                        print(f"      文档 {i+1}: _id={hit['_id']}")
                        source = hit.get('_source', {})
                        print(f"      字段: {list(source.keys())}")
                        if 'id' in source:
                            print(f"      id字段值: {source['id']}")

            # 优化的搜索策略：按成功率排序
            search_strategies = [
                ("match查询", {"match": {"id": memory_id}}),
                ("keyword精确匹配", {"term": {"id.keyword": memory_id}}),
                ("普通term查询", {"term": {"id": memory_id}}),
                ("通配符查询", {"wildcard": {"id": f"*{memory_id}*"}}),
            ]

            # 重试机制处理索引延迟
            for attempt in range(retry_count):
                if attempt > 0:
                    print(f"   🔄 第 {attempt + 1} 次尝试删除...")
                    time.sleep(1)  # 等待索引更新
                    try:
                        client.indices.refresh(index=index_name)
                    except:
                        pass

                for strategy_name, query in search_strategies:
                    try:
                        search_body = {"query": query}
                        response = client.search(index=index_name, body=search_body)
                        hits = response.get('hits', {}).get('hits', [])

                        if hits:
                            print(f"   ✅ 使用{strategy_name}找到 {len(hits)} 个匹配文档")
                            # 删除找到的文档
                            deleted_count = 0
                            for hit in hits:
                                doc_id = hit['_id']
                                client.delete(index=index_name, id=doc_id)
                                deleted_count += 1

                            print(f"   ✅ 成功删除 {deleted_count} 个文档")
                            return True
                        elif verbose and attempt == 0:
                            print(f"   ❌ {strategy_name}未找到匹配文档")

                    except Exception as search_error:
                        if verbose:
                            print(f"   ⚠️ {strategy_name}搜索失败: {str(search_error)[:50]}...")
                        continue

                # 如果第一次尝试失败，显示重试信息
                if attempt == 0 and retry_count > 1:
                    print(f"   ⏳ 未找到记忆，可能是索引延迟，将重试...")

            print(f"   ⚠️ 经过 {retry_count} 次尝试，未找到记忆: {memory_id}")
            return False

        except Exception as e:
            print(f"   ❌ 安全删除失败: {str(e)[:100]}...")
            return False

    def safe_delete_all(self, user_id: str):
        """安全的删除所有记忆方法."""
        try:
            # 尝试直接从 OpenSearch 删除
            client = self.vector_store.client
            index_name = self.collection_name

            # 搜索用户的所有文档
            search_body = {
                "query": {
                    "term": {
                        "user_id": user_id
                    }
                }
            }

            response = client.search(index=index_name, body=search_body)
            hits = response.get('hits', {}).get('hits', [])

            deleted_count = 0
            for hit in hits:
                doc_id = hit['_id']
                client.delete(index=index_name, id=doc_id)
                deleted_count += 1

            print(f"   ✅ 直接从 OpenSearch 删除 {deleted_count} 条记忆")
            return deleted_count

        except Exception as e:
            print(f"   ❌ 安全删除所有记忆失败: {str(e)[:100]}...")
            return 0

    def cleanup_test_data(self):
        """清理所有测试数据."""
        try:
            client = self.vector_store.client
            index_name = self.collection_name

            # 搜索包含测试关键词的文档
            test_keywords = ["测试", "test", "这是一个测试记忆"]
            deleted_count = 0

            for keyword in test_keywords:
                search_body = {
                    "query": {
                        "match": {
                            "payload": keyword
                        }
                    }
                }

                response = client.search(index=index_name, body=search_body)
                hits = response.get('hits', {}).get('hits', [])

                for hit in hits:
                    doc_id = hit['_id']
                    client.delete(index=index_name, id=doc_id)
                    deleted_count += 1
                    print(f"   🗑️ 删除测试文档: {doc_id}")

            # 也尝试删除最近的几条记录（可能是测试数据）
            recent_docs = client.search(
                index=index_name,
                body={
                    "query": {"match_all": {}},
                    "sort": [{"_id": {"order": "desc"}}],
                    "size": 3
                }
            )

            for hit in recent_docs.get('hits', {}).get('hits', []):
                doc_id = hit['_id']
                try:
                    client.delete(index=index_name, id=doc_id)
                    deleted_count += 1
                    print(f"   🗑️ 删除最近文档: {doc_id}")
                except:
                    pass  # 可能已经删除了

            return deleted_count

        except Exception as e:
            print(f"   ❌ 清理测试数据失败: {str(e)[:100]}...")
            return 0